import React from "react";
import EventDetailsBg from "../assets/EventDetailsBg.png";
import { EventDetailslider } from "@/components/EventDetailSlider/EventDetailslider";
import TeamService from "@/components/TeamService/TeamService";
import Footer from "@/components/Footer/Footer";
import fig1 from "../assets/figure3.png";
import SliderOne from "@/components/About/SliderOne";
import SliderThree from "@/components/About/SliderThree";
import WhatOurClientSay from "@/components/WhatOurClientSay/WhatOurClientSay";

const About = () => {
  return (
    <div className="bg-[#fcf2e9] overflow-hidden  ">
      <section
        className="w-full h-screen flex justify-center items-center relative"
        style={{
          backgroundImage: `url(${EventDetailsBg})`,
          backgroundSize: "cover",
          backgroundPosition: "center",
          backgroundRepeat: "no-repeat",
        }}
      >
        <h2
          style={{
            transform: "rotate(-8deg)",
            textShadow: "8px 8px 0px rgba(0,0,0,1)",
          }}
          className="font-Anton text-9xl text-white font-outline-black-1 capitalize text-center"
        >
          <span className="block">ABOUT</span>
          <span className="block">US</span>
        </h2>

        {/* Border lines at the bottom */}
        <div className="absolute bottom-0 left-0 w-full">
          <div className="h-2 bg-[#eb936b]"></div>
          <div className="h-2 bg-[#70b3a3]"></div>
          <div className="h-2 bg-[#f4cd77]"></div>
          <div className="h-2 bg-[#c2ad8c]"></div>
        </div>
      </section>
      <EventDetailslider />
      <div className="h-auto w-screen flex justify-center items-center bg-[#fef5f0]">
        <div className="flex flex-col md:flex-row justify-between items-center w-[90%] gap-[45px] sm:p-[50px] sm:mt-0 mt-6">
          {/* Left Section with Image */}
          <div className="flex-1">
            <img
              src={fig1}
              className="w-full h-full object-cover rounded-lg"
              alt="Brewery Tanks"
            />
          </div>

          {/* Right Section with Text */}
          <div className="flex-1 text-gray-800 ">
            <div className="mb-4">
              <h1 className="text-5xl gap-[10px] font-extrabold text-gray-900 tracking-wide uppercase flex ">
                <span className="block leading-none">OUR</span>
                <span className="block leading-none font-outline-1">STORY</span>
              </h1>
            </div>
            
             <div className="sm:space-y-6 space-y-3 sm:text-[22px] text-[16px]">
              <p className=" leading-relaxed text-gray-700">
              Welcome to Urban Venue, where we make dreams come true. We’re your top choice for event planning and decor, specializing in unforgettable weddings, corporate events, and pool parties. Our unique venues provide the perfect setting for any occasion. At Urban Venue, we love the details and are committed to creativity. We’re all about making your event personal, seamless, and memorable. Let us bring your vision to life!
              </p>
              {/* <p className=" leading-relaxed text-gray-700">
                Our brewery also represents a laboratory where our brewers
                express their creativity, thus developing some of the most
                original and delicious recipes.
              </p>
              <p className=" leading-relaxed text-gray-700">
                Convinced that innovative beer recipes deserve to be appreciated
                by all, we are committed to making craft beer accessible and
                introducing this extraordinary world to as many people as
                possible.
              </p> */}
            </div>
          </div>
        </div>
      </div>
      <TeamService />
      <SliderOne />
      <WhatOurClientSay />
      <SliderThree />
      <Footer />
    </div>
  );
};

export default About;
