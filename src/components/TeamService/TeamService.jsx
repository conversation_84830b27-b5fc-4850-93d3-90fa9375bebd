import React from "react";
import { Swiper, SwiperSlide } from "swiper/react";
import "swiper/css";
import "swiper/css/autoplay";
import { Autoplay, Navigation } from "swiper/modules";
import { useRef } from "react";
import ArrowBackIosIcon from "@mui/icons-material/ArrowBackIos";
import ArrowForwardIosIcon from "@mui/icons-material/ArrowForwardIos";
import section5 from "../../assets/section5latest.jfif";
import team1 from "../../assets/teamImg.jpg";
import team2 from "../../assets/teamImg.jpg";
import team3 from "../../assets/teamImg.jpg";
import { Link } from "react-router-dom";
const TeamService = () => {
  const prevRef = useRef(null);
  const nextRef = useRef(null);
  const teamMembers = [
    {
      id: 1,
      name: "<PERSON>",
      role: "Designer",
      image: team1,
    },
    {
      id: 2,
      name: "<PERSON>",
      role: "<PERSON><PERSON><PERSON>",
      image: team2,
    },
    {
      id: 3,
      name: "<PERSON>",
      role: "Project Manager",
      image: team3,
    },
    {
      id: 4,
      name: "<PERSON>",
      role: "Project Manager",
      image: team1,
    },
    // Add more team members as needed
  ];
  return (
    <div className="w-screen h-[130vh] flex bg-[#ea9368]">
      {/* Left Section */}
      <div className="hidden lg:flex lg:w-1/2 lg:justify-start lg:items-center">
        <img
          src={section5}
          alt="Team Service"
          className="w-[85%] h-[90%] rounded-r-[200px]"
        />
      </div>

      {/* Right Section */}
      <div className="sm:w-1/2 flex flex-col justify-start items-start p-8 w-[100%] ">
        {/* Heading */}
        <h1 className=" font-Anton text-white tracking-wide  text-6xl font-bold mb-4">
          THE HISTORY OF{" "}
        </h1>
        <h1 className=" font-Anton text-transparent font-outline-white-1 tracking-wide  text-6xl font-bold mb-4">
          {" "}
          THE SERVICE
        </h1>

        {/* Paragraph */}
        <p className="font-archivo text-[15px] text-white font-normal leading-[35px] tracking-[1px]">
          There are many variations of passages of Lorem Ipsum available, but
          the majority have suffered alteration in some form, by injected
          humour, or randomised words which don't look even.
        </p>
        <p className="font-archivo text-[15px] text-white font-normal leading-[35px] tracking-[1px]">
          There are many variations of passages of Lorem Ipsum available, but
          the majority have suffered alteration in some form,
        </p>
        <br />
        <Link to="/about">
          <button className=" shadow-custom px-8 py-2 bg-white border border-black font-normal tracking-wide rounded-sm font-Anton ">
            READ MORE
          </button>
        </Link>
        <br />

        <div className="w-full  rounded-lg mt-4">
          <h1 className=" font-Anton text-white tracking-wide  text-4xl font-bold mb-4">
            {" "}
            THE TEAM
          </h1>

          <Swiper
            spaceBetween={20}
            slidesPerView={3}
            autoplay={{ delay: 3000, disableOnInteraction: false }}
            modules={[Autoplay, Navigation]}
            className="w-full "
            navigation={{
              prevEl: prevRef.current,
              nextEl: nextRef.current,
            }}
            onBeforeInit={(swiper) => {
              swiper.params.navigation.prevEl = prevRef.current;
              swiper.params.navigation.nextEl = nextRef.current;
            }}
          >
            {teamMembers.map((member) => (
              <SwiperSlide
                key={member.id}
                className="relative flex flex-col items-center group "
              >
                <img
                  src={member.image}
                  alt={member.name}
                  className="w-full aspect-square rounded-xl object-cover mb-4 overflow-hidden"
                />

                {/* Overlay container */}
                <div className="absolute inset-0 bg-black bg-opacity-50 rounded-xl flex flex-col h-55 items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity mb-4 duration-300">
                  <h2 className="text-white text-xl font-semibold">
                    {member.name}
                  </h2>
                  <p className="text-gray-300">{member.role}</p>
                </div>
              </SwiperSlide>
            ))}
          </Swiper>
          <br />
          {/* Custom navigation buttons */}
          <button
            ref={prevRef}
            className="  text-white rounded-full p-3 m-1 bg-white "
          >
            <ArrowBackIosIcon className="text-black ml-2" />
          </button>
          <button
            ref={nextRef}
            className=" text-white  rounded-full p-3  m-1 bg-white"
          >
            <ArrowForwardIosIcon className="text-black ml-3" />
          </button>
        </div>
      </div>
    </div>
  );
};

export default TeamService;
