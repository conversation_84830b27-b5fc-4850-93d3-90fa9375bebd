import { cn } from '../../lib/utils'
import Marquee from '../ui/marquee'
import figure1 from "../../assets/Figure1.png"
import figure2 from "../../assets/Figure2.png"
import figure3 from "../../assets/figure3.png"
const reviews = [
  {
    img: figure1,
  },
  {
    img: figure2,
  },
  {
    img: figure3,
  },
  {
    name: 'HALDI ',
  },
  {
    name: 'MEHNDI ' ,
  },
  {
    name: 'VENUES',
  },
]

const ReviewCard = ({ img, name }) => {
  return (
    <figure className="flex justify-center items-center relative h-24 w-96 cursor-pointer overflow-hidden rounded-xl">
      {img && (
        <img
          className="rounded-full object-cover w-full h-full"
          alt=""
          src={img}
        />
      )}
      {name && !img && (
        <figcaption
          style={{
            // color: '#ed926b',
            textAlign: 'center',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            textShadow: `
              -1px -1px 0 #be724d,
              1px -1px 0 #be724d,
              -1px 1px 0 #be724d,
              1px 1px 0 #be724d
            `,
          }}
          className="text-8xl font-Anton font-medium dark:text-white"
        >
          {name}
        </figcaption>
      )}
    </figure>
  )
}

export function HomeSliderText() {
  const filteredReviews = reviews.filter((review) => review.img || review.name)

  return (
    <div className="relative flex min-h-[600px] w-full flex-col items-center justify-center overflow-hidden rounded-lg border bg-[#fcf2e9] m">
      <Marquee pauseOnHover className="[--duration:20s] mt-[-40px]" style={{ color: '#e99468' }}>
        {filteredReviews.map((review) => (
          <ReviewCard key={review.img || review.name} {...review} />
        ))}
      </Marquee>
      <Marquee reverse pauseOnHover className="[--duration:20s] mt-[-40px]" style={{ color: '#6cb3a6' }}>
        {filteredReviews.slice(1).map((review) => (
          <ReviewCard key={review.img || review.name} {...review} />
        ))}
      </Marquee>
      <Marquee pauseOnHover className="[--duration:20s] mt-[-40px]" style={{ color: '#f0d075' }}>
        {filteredReviews.map((review) => (
          <ReviewCard key={review.img || review.name} {...review} />
        ))}
      </Marquee>
      <Marquee reverse pauseOnHover className="[--duration:20s] mt-[-40px]" style={{ color: '#c3ab89' }}>
        {filteredReviews.slice(1).map((review) => (
          <ReviewCard key={review.img || review.name} {...review} />
        ))}
      </Marquee>
      {/* <div className="pointer-events-none absolute inset-y-0 left-0 w-1/3 bg-gradient-to-r from-white dark:from-background"></div> */}
      {/* <div className="pointer-events-none absolute inset-y-0 right-0 w-1/3 bg-gradient-to-l from-white dark:from-background"></div> */}
    </div>
  )
}
