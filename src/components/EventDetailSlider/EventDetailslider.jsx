import { cn } from '../../lib/utils'
import Marquee from '../ui/marquee'
import figure1 from '../../assets/Figure1.png'
import figure2 from '../../assets/Figure2.png'
import figure3 from '../../assets/figure3.png'
const reviews = [
  {
    img: figure1,
  },
  {
    name: 'ENGAGEMENT CEREMONY ',
  },
  {
    img: figure2,
  },
  {
    name: 'ENGAGEMENT CEREMONY ',
  },
  {
    img: figure3,
  },

  {
    name: 'ENGAGEMENT CEREMONY',
  },
  {
    img: figure3,
  },
]

const ReviewCard = ({ img, name, isTransparent }) => {
    return (
      <figure className="flex justify-center items-center relative h-24 min-w-96 cursor-pointer overflow-hidden rounded-xl">
        {img && (
          <img
            className="rounded-full object-cover w-full h-full"
            alt=""
            src={img}
          />
        )}
        {name && !img && (
          <figcaption
            style={{
              textAlign: 'center',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
            //   textShadow: `-1px -1px 0 #be724d, 1px -1px 0 #be724d, -1px 1px 0 #be724d, 1px 1px 0 #be724d`,
              color: isTransparent ? 'transparent' : 'inherit', // Apply transparency conditionally
            }}
            className="text-8xl font-Anton font-medium dark:text-white font-outline-black-1"
          >
            {name}
          </figcaption>
        )}
      </figure>
    )
  }
  
  export function EventDetailslider() {
    const filteredReviews = reviews.filter((review) => review.img || review.name)
  
    return (
      <div className="relative flex min-h-[300px] w-full flex-col items-center justify-center overflow-hidden rounded-lg border bg-[#fcf2e9] ">
        <Marquee
          pauseOnHover
          className="[--duration:40s]"
          style={{ color: '#000000' }}
        >
          {filteredReviews.map((review, index) => (
            <ReviewCard
              key={review.img || review.name}
              {...review}
              isTransparent={index % 2 !== 0} // Make alternate text transparent
            />
          ))}
        </Marquee>
        <Marquee
          reverse
          pauseOnHover
          className="[--duration:40s]"
        >
          {filteredReviews.slice(1).map((review, index) => (
            <ReviewCard
              key={review.img || review.name}
              {...review}
              isTransparent={index % 2 !== 0} // Make alternate text transparent
            />
          ))}
        </Marquee>
  
        {/* <div className="pointer-events-none absolute inset-y-0 left-0 w-1/4 bg-gradient-to-r from-white dark:from-background"></div> */}
        {/* <div className="pointer-events-none absolute inset-y-0 right-0 w-1/4 bg-gradient-to-l from-white dark:from-background"></div> */}
      </div>
    )
  }
  