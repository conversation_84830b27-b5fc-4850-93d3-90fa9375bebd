import React, { useState } from 'react';
import wpp1 from "../../assets/wpp1.png";
import feedback1 from "../../assets/feedbackNeha.jpg";
import feedback2 from "../../assets/feedbackRishab.jpg";
import feedback3 from "../../assets/review1.jpeg";
import feedback4 from "../../assets/review2.jpeg";

const WhatOurClientSay = () => {
  // Example slides data
  const slides = [
    {
      image: feedback4,
      head: "<PERSON><PERSON><PERSON>",
      text: 'A truly beautiful and magical celebration! The wedding was an unforgettable experience, filled with love, joy, and perfectly planned moments that reflected the unique personality of the couple. Every detail, from the stunning venue to the thoughtful decorations, was meticulously crafted to create a warm and inviting atmosphere. The ceremony was heartwarming and sincere, with heartfelt vows that truly captured the essence of the love shared by the couple. The officiant’s words were both inspiring and personal, making everyone in attendance feel emotionally connected to the union being celebrated. It was evident how much love and hard work went into planning this incredible event. The speeches, especially from close family and friends, added a deeply personal touch, bringing laughter, tears, and a sense of togetherness that will be remembered for years to come.',
    },
    {
      image: feedback2,
      head: "<PERSON><PERSON><PERSON>",
      text: 'What stood out the most was the couple’s radiant happiness throughout the day. Their love for each other shone brightly, and their connection was inspiring to everyone who witnessed it. It was such a joy to see their vision for the day come to life so beautifully.Thank you for including us in such a special moment in your lives. The wedding was not only a celebration of your love but also a testament to the strength of the bond you share and the support of the community around you. We wish you both a lifetime of happiness, love, and endless adventures together.',
    },
    {
      image: feedback3,
      head: "Pooja Verma",
      text: 'A truly beautiful and magical celebration! The wedding was an unforgettable experience, filled with love, joy, and perfectly planned moments that reflected the unique personality of the couple. Every detail, from the stunning venue to the thoughtful decorations, was meticulously crafted to create a warm and inviting atmosphere. The ceremony was heartwarming and sincere, with heartfelt vows that truly captured the essence of the love shared by the couple. The officiant’s words were both inspiring and personal, making everyone in attendance feel emotionally connected to the union being celebrated. It was evident how much love and hard work went into planning this incredible event. The speeches, especially from close family and friends, added a deeply personal touch, bringing laughter, tears, and a sense of togetherness that will be remembered for years to come.',    },
  ];

  const [currentSlide, setCurrentSlide] = useState(0);

  // Slide navigation handlers
  const prevSlide = () => {
    setCurrentSlide((prevIndex) => (prevIndex === 0 ? slides.length - 1 : prevIndex - 1));
  };

  const nextSlide = () => {
    setCurrentSlide((prevIndex) => (prevIndex === slides.length - 1 ? 0 : prevIndex + 1));
  };

  return (
    <div className="w-screen sm:mt-0 mt-[20px] sm:h-screen flex flex-col items-center justify-center bg-gray-100">
      {/* Slide container */}
      <div className="relative flex items-center justify-between w-11/12 h-5/6  shadow-lg rounded-lg overflow-hidden bg-[#f1cf76] p-4">
        {/* Slide content */}
        <div className="flex sm:flex-row flex-col w-full h-full">
          {/* Image on the left */}
          <div className="sm:w-1/2 pt-6 pb-6 h-full w-full ">
            <img
              src={slides[currentSlide].image}
              alt="Client"
              className="object-contain w-full h-full"
            />
          </div>

          {/* Text on the right */}
          <div className="sm:w-1/2 w-full flex flex-col justify-start sm:p-8 p-0 ">
            <h2 className="text-6xl font-bold mb-4 text-transparent font-outline-black-1">
              {slides[currentSlide].head}
            </h2>
            <p className="text-lg text-gray-700">{slides[currentSlide].text}</p>
          </div>
        </div>
      </div>

      {/* Navigation buttons */}
      <div className="flex justify-center space-x-8 mt-4">
        <button
          onClick={prevSlide}
          className="text-xl text-white bg-gray-700 px-6 py-2 rounded-full shadow-md hover:bg-gray-800 transition"
        >
          Prev
        </button>
        <button
          onClick={nextSlide}
          className="text-xl text-white bg-gray-700 px-6 py-2 rounded-full shadow-md hover:bg-gray-800 transition"
        >
          Next
        </button>
      </div>
    </div>
  );
};

export default WhatOurClientSay;
